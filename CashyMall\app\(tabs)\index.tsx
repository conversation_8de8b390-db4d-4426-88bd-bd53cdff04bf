import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  useColorScheme,
  ActivityIndicator,
  Alert
} from 'react-native';
import { Link } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Header from '../../components/Header';
import ProductCard from '../../components/ProductCard';
import CategoryCard from '../../components/CategoryCard';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { getFeaturedProducts, getRootCategories } from '../../services/api/product';
import { addToCart } from '../../services/api/cart';
import { Product, Category } from '../../types/product';
import { useCartWishlist } from '../../context/CartWishlistContext';

// Default image URLs for categories without images
const DEFAULT_CATEGORY_IMAGES = {
  'Electronics': 'https://images.unsplash.com/photo-1498049794561-7780e7231661?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
  'Home & Kitchen': 'https://images.unsplash.com/photo-1556911220-bff31c812dba?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1168&q=80',
  'Clothing': 'https://images.unsplash.com/photo-1567401893414-76b7b1e5a7a5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
  'Beauty & Personal Care': 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
  'default': 'https://images.unsplash.com/photo-1472851294608-062f824d29cc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80'
};

export default function HomeScreen() {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  // Use context for real-time cart updates
  const { addToCartCount } = useCartWishlist();

  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loadingProducts, setLoadingProducts] = useState(true);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch featured products (random selection from all products)
  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      setLoadingProducts(true);
      try {
        const response = await getFeaturedProducts(4); // Limit to 4 featured products
        console.log('Featured Products Response:', JSON.stringify(response, null, 2));

        if (response.data && Array.isArray(response.data)) {
          // If response.data is already an array, use it directly
          setFeaturedProducts(response.data);
          setError(null);
        } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
          // If response.data.data is an array (nested data structure)
          setFeaturedProducts(response.data.data);
          setError(null);
        } else {
          setError('Invalid featured products data format received from API');
          console.error('Invalid featured products data format:', response);
        }
      } catch (err) {
        setError('An error occurred while fetching featured products');
        console.error(err);
      } finally {
        setLoadingProducts(false);
      }
    };

    fetchFeaturedProducts();
  }, []);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      setLoadingCategories(true);
      try {
        const response = await getRootCategories();
        console.log('Categories Response:', JSON.stringify(response, null, 2));

        if (response.data && response.data.data && Array.isArray(response.data.data)) {
          // Filter out inactive categories
          const activeCategories = response.data.data.filter(category => category.active);
          setCategories(activeCategories);
          setError(null);
        } else {
          console.error('Invalid categories data format:', response);
        }
      } catch (err) {
        console.error('Error fetching categories:', err);
        // Don't set error state for categories to avoid blocking the UI
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  const handleAddToCart = async (productId: string) => {
    try {
      const response = await addToCart(productId);
      if (response.error) {
        Alert.alert('Error', 'Failed to add item to cart');
        return;
      }

      // Update cart count immediately
      addToCartCount(1);
      Alert.alert('Success', 'Item added to cart');
    } catch (err) {
      console.error('Error adding to cart:', err);
      Alert.alert('Error', 'Failed to add item to cart');
    }
  };

  // Create style objects with dynamic properties to avoid array styles for web
  const containerStyle = {
    ...styles.container,
    backgroundColor: colors.background
  };

  const heroBannerButtonStyle = {
    ...styles.heroBannerButton,
    backgroundColor: colors.primary
  };

  const sectionTitleStyle = {
    ...styles.sectionTitle,
    color: colors.text
  };

  const sectionLinkStyle = {
    ...styles.sectionLink,
    color: colors.primary
  };

  const newsletterSectionStyle = {
    ...styles.newsletterSection,
    backgroundColor: colors.cardBackground
  };

  const newsletterTitleStyle = {
    ...styles.newsletterTitle,
    color: colors.text
  };

  const newsletterSubtitleStyle = {
    ...styles.newsletterSubtitle,
    color: colors.tabIconDefault
  };

  return (
    <View style={containerStyle}>
      <Header showLogo={true} />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Hero Banner */}
        <View style={styles.heroBanner}>
          <Image
            source={{ uri: 'https://images.unsplash.com/photo-1483985988355-763728e1935b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80' }}
            style={styles.heroBannerImage}
            resizeMode="cover"
          />
          <View style={styles.heroBannerOverlay}>
            <Text style={styles.heroBannerTitle}>Summer Collection 2025</Text>
            <Text style={styles.heroBannerSubtitle}>Discover our latest arrivals perfect for the summer season</Text>
            <Link href="/shop" asChild>
              <TouchableOpacity style={heroBannerButtonStyle}>
                <Text style={styles.heroBannerButtonText}>Shop Now</Text>
                <Ionicons name="arrow-forward" size={16} color="#ffffff" />
              </TouchableOpacity>
            </Link>
          </View>
        </View>

        {/* Shop Categories */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={sectionTitleStyle}>Shop Categories</Text>
            <Link href="/shop" asChild>
              <TouchableOpacity>
                <Text style={sectionLinkStyle}>See All</Text>
              </TouchableOpacity>
            </Link>
          </View>

          {loadingCategories ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.text }]}>
                Loading categories...
              </Text>
            </View>
          ) : categories.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={[styles.emptyText, { color: colors.text }]}>
                No categories available
              </Text>
            </View>
          ) : (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.categoriesContainer}
            >
              {categories.map((category) => {
                // Get default image based on category type or name, or use generic default
                const defaultImage =
                DEFAULT_CATEGORY_IMAGES[category.type as keyof typeof DEFAULT_CATEGORY_IMAGES] ||
                (DEFAULT_CATEGORY_IMAGES as Record<string, string>)[category.name] ||
                  DEFAULT_CATEGORY_IMAGES.default;

                // Use API image if available, otherwise use default
                const imageUrl = category.imageUrl || defaultImage;

                return (
                  <View style={styles.categoryItem} key={category.id}>
                    <CategoryCard
                      id={category.id.toString()}
                      name={category.name}
                      imageUrl={imageUrl}
                      productCount={category.productCount}
                    />
                  </View>
                );
              })}
            </ScrollView>
          )}
        </View>

        {/* Featured Products */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={sectionTitleStyle}>Featured Products</Text>
            <Link href="/shop" asChild>
              <TouchableOpacity>
                <Text style={sectionLinkStyle}>See All</Text>
              </TouchableOpacity>
            </Link>
          </View>

          {loadingProducts ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.text }]}>
                Loading featured products...
              </Text>
            </View>
          ) : error ? (
            <View style={styles.errorContainer}>
              <Ionicons name="alert-circle-outline" size={32} color={colors.error} />
              <Text style={[styles.errorText, { color: colors.text }]}>
                {error}
              </Text>
            </View>
          ) : featuredProducts.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={[styles.emptyText, { color: colors.text }]}>
                No featured products available
              </Text>
            </View>
          ) : (
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.productsScrollContainer}
            >
              {featuredProducts.map((product) => (
                <View style={styles.productItem} key={product.id}>
                  <ProductCard
                    id={product.id.toString()}
                    name={product.name}
                    price={product.price}
                    discountPrice={product.discountPrice || undefined}
                    imageUrl={product.imageUrl}
                    sizes={product.sizes}
                    colors={product.colors}
                    isNew={product.tags?.some(tag => tag.name === 'New Arrival')}
                    onAddToCart={() => handleAddToCart(product.id.toString())}
                  />
                </View>
              ))}
            </ScrollView>
          )}
        </View>

        {/* Newsletter Subscription */}
        <View style={newsletterSectionStyle}>
          <Text style={newsletterTitleStyle}>Stay in the Loop</Text>
          <Text style={newsletterSubtitleStyle}>
            Subscribe to our newsletter for exclusive offers and early access to new arrivals
          </Text>
          {/* Newsletter form would go here */}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  heroBanner: {
    height: 200,
    marginLeft:10,
    width: Layout.window.width - 20,
    position: 'relative',

  },
  heroBannerImage: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
  },
  heroBannerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    padding: Layout.spacing.md,
    justifyContent: 'center',
    borderRadius: 20,
  },
  heroBannerTitle: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.xs,
  },
  heroBannerSubtitle: {
    color: '#ffffff',
    fontSize: 14,
    marginBottom: Layout.spacing.md,
  },
  heroBannerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Layout.spacing.xs,
    paddingHorizontal: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    alignSelf: 'flex-start',
  },
  heroBannerButtonText: {
    color: '#ffffff',
    fontWeight: '600',
    marginRight: Layout.spacing.xs,
  },
  section: {
    padding: Layout.spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  sectionLink: {
    fontSize: 14,
    fontWeight: '500',
  },
  categoriesContainer: {
    paddingRight: Layout.spacing.md,
  },
  categoryItem: {
    width: 280,
    marginRight: Layout.spacing.md,
  },
  productsGrid: {
    flexDirection: 'row',
    gap: 10,
    justifyContent: 'space-between',
  },
  productsScrollContainer: {
    paddingRight: Layout.spacing.md,
  },
  productItem: {
    width: 180,
    marginRight: Layout.spacing.md,
  },
  newsletterSection: {
    padding: Layout.spacing.md,
    marginHorizontal: Layout.spacing.md,
    marginBottom: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    alignItems: 'center',
  },
  newsletterTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.xs,
    textAlign: 'center',
  },
  newsletterSubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: Layout.spacing.md,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.xl,
    minHeight: 200,
  },
  loadingText: {
    fontSize: 16,
    marginTop: Layout.spacing.md,
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.xl,
    minHeight: 200,
  },
  errorText: {
    fontSize: 16,
    marginTop: Layout.spacing.md,
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.xl,
    minHeight: 200,
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
  },
});
