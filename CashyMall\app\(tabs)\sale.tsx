import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Image,
  TouchableOpacity,
  useColorScheme,
  Alert,
} from 'react-native';
import { Link } from 'expo-router';
import Header from '../../components/Header';
import ProductCard from '../../components/ProductCard';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { getSalesProducts } from '@/services/api/product';
import { addToCart } from '@/services/api/cart';

// Sale banners
const saleBanners = [
  {
    id: '1',
    title: 'Summer Sale',
    subtitle: 'Up to 50% off',
    imageUrl:
      'https://images.unsplash.com/photo-1607083206968-13611e3d76db?ixlib=rb-4.0.3&auto=format&fit=crop&w=1215&q=80',
    backgroundColor: '#f9a8d4',
    textColor: '#ffffff',
  },
  {
    id: '2',
    title: 'Flash Sale',
    subtitle: 'Limited time offers',
    imageUrl:
      'https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80',
    backgroundColor: '#93c5fd',
    textColor: '#ffffff',
  },
];

export default function SaleScreen() {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];

  const [products, setproducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchproducts = async () => {
      try {
        const response = await getSalesProducts({
          page: 0,
          size: 50,
          sortBy: 'name',
          direction: 'asc',
        });
        setproducts(response?.data.data.content || []);
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };
    console.log('products sales', products);
    fetchproducts();
  }, []);

  const handleAddToCart = async (id: string) => {
    try {
      const response = await addToCart(id);
      if (response.error) {
        Alert.alert('Error', 'Failed to add item to cart');
        return;
      }
      Alert.alert('Success', 'Item added to cart');
    } catch (err) {
      console.error('Error adding to cart:', err);
      Alert.alert('Error', 'Failed to add item to cart');
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header title="Sale" />

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Countdown */}
        <View style={[styles.countdownContainer, { backgroundColor: colors.primary }]}>
          <Text style={styles.countdownTitle}>Limited Time Offers</Text>
          <View style={styles.countdownTimerContainer}>
            {['02', '12', '45', '30'].map((num, index) => (
              <React.Fragment key={index}>
                <View style={styles.countdownItem}>
                  <Text style={styles.countdownNumber}>{num}</Text>
                  <Text style={styles.countdownLabel}>
                    {['Days', 'Hours', 'Mins', 'Secs'][index]}
                  </Text>
                </View>
                {index < 3 && <Text style={styles.countdownSeparator}>:</Text>}
              </React.Fragment>
            ))}
          </View>
        </View>

        {/* Banners */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.bannersContainer}
        >
          {saleBanners.map((banner) => (
            <TouchableOpacity
              key={banner.id}
              style={[styles.bannerCard, { backgroundColor: banner.backgroundColor }]}
            >
              <View style={styles.bannerContent}>
                <Text style={[styles.bannerTitle, { color: banner.textColor }]}>
                  {banner.title}
                </Text>
                <Text style={[styles.bannerSubtitle, { color: banner.textColor }]}>
                  {banner.subtitle}
                </Text>
                <TouchableOpacity style={styles.bannerButton}>
                  <Text style={styles.bannerButtonText}>Shop Now</Text>
                </TouchableOpacity>
              </View>
              <Image source={{ uri: banner.imageUrl }} style={styles.bannerImage} resizeMode="cover" />
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Sale Products */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>Top Deals</Text>
            <Link href="/shop" asChild>
              <TouchableOpacity>
                <Text style={[styles.sectionLink, { color: colors.primary }]}>See All</Text>
              </TouchableOpacity>
            </Link>
          </View>

          {loading ? (
            <Text style={{ textAlign: 'center', marginVertical: 20 }}>Loading deals...</Text>
          ) : products.length === 0 ? (
            <Text style={{ textAlign: 'center', marginVertical: 20 }}>No sales found.</Text>
          ) : (
            <View style={styles.productsGrid}>
              {products.map((brand) => (
                <ProductCard
                  key={brand.id}
                  id={brand.id}
                  name={brand.name}
                  price={brand.price}
                  discountPrice={brand.discountPrice}
                  imageUrl={brand.imageUrl}
                  sizes={brand.sizes}
                  colors={brand.colors}
                  isNew={brand.isNew ?? false}
                  onAddToCart={() => handleAddToCart(brand.id)}
                />
              ))}
            </View>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1 },
  scrollView: { flex: 1 },
  countdownContainer: {
    padding: Layout.spacing.md,
    margin: Layout.spacing.md,
    borderRadius: Layout.borderRadius.md,
    alignItems: 'center',
  },
  countdownTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.sm,
  },
  countdownTimerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  countdownItem: { alignItems: 'center' },
  countdownNumber: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  countdownLabel: {
    color: '#ffffff',
    fontSize: 12,
  },
  countdownSeparator: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
    marginHorizontal: Layout.spacing.xs,
  },
  bannersContainer: {
    paddingHorizontal: Layout.spacing.md,
    paddingBottom: Layout.spacing.md,
  },
  bannerCard: {
    width: 280,
    height: 150,
    borderRadius: Layout.borderRadius.md,
    marginRight: Layout.spacing.md,
    overflow: 'hidden',
    flexDirection: 'row',
  },
  bannerContent: {
    flex: 1,
    padding: Layout.spacing.md,
    justifyContent: 'center',
  },
  bannerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: Layout.spacing.xs,
  },
  bannerSubtitle: {
    fontSize: 14,
    marginBottom: Layout.spacing.sm,
  },
  bannerButton: {
    backgroundColor: '#ffffff',
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.md,
    alignSelf: 'flex-start',
  },
  bannerButtonText: {
    color: '#000000',
    fontSize: 12,
    fontWeight: '600',
  },
  bannerImage: {
    width: 120,
    height: '100%',
  },
  section: {
    padding: Layout.spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Layout.spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  sectionLink: {
    fontSize: 14,
    fontWeight: '500',
  },
  productsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
});
