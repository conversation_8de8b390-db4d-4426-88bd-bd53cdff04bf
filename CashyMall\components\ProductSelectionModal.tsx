import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  useColorScheme,
  Image,
  ScrollView,
  Alert
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Colors from '../constants/Colors';
import Layout from '../constants/Layout';
import Button from './Button';
import { addToCartWithOptions } from '../services/api/cart';
import { useCartWishlist } from '../context/CartWishlistContext';

interface ProductSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  product: {
    id: string;
    name: string;
    price: number;
    discountPrice?: number;
    imageUrl: string;
    sizes?: string | null;
    colors?: string;
  };
  onSuccess?: () => void;
}

export default function ProductSelectionModal({
  visible,
  onClose,
  product,
  onSuccess
}: ProductSelectionModalProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const { addToCartCount } = useCartWishlist();

  const [quantity, setQuantity] = useState(1);
  const [selectedSize, setSelectedSize] = useState<string>('');
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  // Parse sizes and colors from product data
  const availableSizes = product.sizes 
    ? product.sizes.split(',').map(size => size.trim()).filter(size => size.length > 0)
    : [];
  
  const availableColors = product.colors 
    ? product.colors.split(',').map(color => color.trim()).filter(color => color.length > 0)
    : [];

  // Reset selections when modal opens
  useEffect(() => {
    if (visible) {
      setQuantity(1);
      setSelectedSize(availableSizes.length > 0 ? availableSizes[0] : '');
      setSelectedColor(availableColors.length > 0 ? availableColors[0] : '');
    }
  }, [visible, product.id]);

  const handleIncrement = () => {
    setQuantity(prev => prev + 1);
  };

  const handleDecrement = () => {
    if (quantity > 1) {
      setQuantity(prev => prev - 1);
    }
  };

  const handleAddToCart = async () => {
    setIsLoading(true);
    
    try {
      const response = await addToCartWithOptions(
        product.id,
        quantity,
        availableSizes.length > 0 ? selectedSize : undefined,
        availableColors.length > 0 ? selectedColor : undefined
      );
      
      if (response.error) {
        Alert.alert('Error', 'Failed to add item to cart');
        return;
      }

      // Update cart count immediately
      addToCartCount(quantity);
      
      // Show success message
      Alert.alert('Success', 'Item added to cart');
      
      // Call success callback and close modal
      onSuccess && onSuccess();
      onClose();
      
    } catch (err) {
      console.error('Error adding to cart:', err);
      Alert.alert('Error', 'Failed to add item to cart');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate display price
  const safePrice = typeof product.price === 'number' ? product.price : 0;
  const safeDiscountPrice = typeof product.discountPrice === 'number' ? product.discountPrice : null;
  const hasDiscount = safeDiscountPrice !== null && safeDiscountPrice < safePrice;
  const finalPrice = hasDiscount ? (safePrice - (safePrice * safeDiscountPrice / 100)) : safePrice;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContainer, { backgroundColor: colors.cardBackground }]}>
          {/* Header */}
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Add to Cart</Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>

          <ScrollView contentContainerStyle={styles.modalContent}>
            {/* Product Info */}
            <View style={styles.productInfo}>
              <Image
                source={{ uri: product.imageUrl }}
                style={styles.productImage}
                resizeMode="cover"
              />
              <View style={styles.productDetails}>
                <Text style={[styles.productName, { color: colors.text }]} numberOfLines={2}>
                  {product.name}
                </Text>
                <Text style={[styles.productPrice, { color: colors.primary }]}>
                  ${finalPrice.toFixed(2)}
                  {hasDiscount && (
                    <Text style={[styles.originalPrice, { color: colors.tabIconDefault }]}>
                      {' '}${safePrice.toFixed(2)}
                    </Text>
                  )}
                </Text>
              </View>
            </View>

            {/* Size Selection */}
            {availableSizes.length > 0 && (
              <View style={styles.selectionSection}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>Size</Text>
                <View style={styles.optionsContainer}>
                  {availableSizes.map((size) => (
                    <TouchableOpacity
                      key={size}
                      style={[
                        styles.optionButton,
                        { borderColor: colors.border },
                        selectedSize === size && {
                          backgroundColor: colors.primary,
                          borderColor: colors.primary
                        }
                      ]}
                      onPress={() => setSelectedSize(size)}
                    >
                      <Text
                        style={[
                          styles.optionText,
                          { color: colors.text },
                          selectedSize === size && { color: '#ffffff' }
                        ]}
                      >
                        {size}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}

            {/* Color Selection */}
            {availableColors.length > 0 && (
              <View style={styles.selectionSection}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>Color</Text>
                <View style={styles.optionsContainer}>
                  {availableColors.map((color) => (
                    <TouchableOpacity
                      key={color}
                      style={[
                        styles.optionButton,
                        { borderColor: colors.border },
                        selectedColor === color && {
                          backgroundColor: colors.primary,
                          borderColor: colors.primary
                        }
                      ]}
                      onPress={() => setSelectedColor(color)}
                    >
                      <Text
                        style={[
                          styles.optionText,
                          { color: colors.text },
                          selectedColor === color && { color: '#ffffff' }
                        ]}
                      >
                        {color}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            )}

            {/* Quantity Selection */}
            <View style={styles.selectionSection}>
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Quantity</Text>
              <View style={[styles.quantityContainer, { borderColor: colors.border }]}>
                <TouchableOpacity
                  style={[styles.quantityButton, { borderRightColor: colors.border }]}
                  onPress={handleDecrement}
                  disabled={quantity <= 1}
                >
                  <Ionicons
                    name="remove"
                    size={20}
                    color={quantity <= 1 ? colors.tabIconDefault : colors.text}
                  />
                </TouchableOpacity>

                <Text style={[styles.quantity, { color: colors.text }]}>
                  {quantity}
                </Text>

                <TouchableOpacity
                  style={[styles.quantityButton, { borderLeftColor: colors.border }]}
                  onPress={handleIncrement}
                >
                  <Ionicons name="add" size={20} color={colors.text} />
                </TouchableOpacity>
              </View>
            </View>
          </ScrollView>

          {/* Add to Cart Button */}
          <View style={styles.buttonContainer}>
            <Button
              title={isLoading ? "Adding..." : "Add to Cart"}
              onPress={handleAddToCart}
              disabled={isLoading}
              style={styles.addButton}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    borderTopLeftRadius: Layout.borderRadius.lg,
    borderTopRightRadius: Layout.borderRadius.lg,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Layout.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  modalContent: {
    padding: Layout.spacing.md,
  },
  productInfo: {
    flexDirection: 'row',
    marginBottom: Layout.spacing.lg,
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: Layout.borderRadius.md,
    marginRight: Layout.spacing.md,
  },
  productDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  productName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: Layout.spacing.xs,
  },
  productPrice: {
    fontSize: 18,
    fontWeight: '600',
  },
  originalPrice: {
    fontSize: 14,
    textDecorationLine: 'line-through',
  },
  selectionSection: {
    marginBottom: Layout.spacing.lg,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: Layout.spacing.sm,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Layout.spacing.sm,
  },
  optionButton: {
    paddingHorizontal: Layout.spacing.md,
    paddingVertical: Layout.spacing.sm,
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    minWidth: 50,
    alignItems: 'center',
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  quantityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    width: 120,
  },
  quantityButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0,
  },
  quantity: {
    flex: 1,
    textAlign: 'center',
    fontSize: 16,
    fontWeight: '500',
  },
  buttonContainer: {
    padding: Layout.spacing.md,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  addButton: {
    width: '100%',
  },
});
