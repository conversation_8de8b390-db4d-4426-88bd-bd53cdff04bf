/**
 * Cart API service for the CashyMall mobile app
 */
import { get, post, put, del } from './index';
import { CART_ENDPOINTS } from './endpoints';
import { CartResponse, Product } from '../../types/product';



/**
 * Get user's cart items
 * @returns List of items in user's cart
 */
export async function getCart() {
  const response = await get<CartResponse>(CART_ENDPOINTS.GET);
  return response;
}

/**
 * Add item to cart
 * @param productId Product ID
 * @param quantity Quantity to add
 */
export async function addToCart(productId: string, quantity: number = 1) {
  const response = await post(CART_ENDPOINTS.ADD, {
    productId,
    quantity
  });
  return response;
}

/**
 * Add item to cart with size and color options
 * @param productId Product ID
 * @param quantity Quantity to add
 * @param size Optional size parameter
 * @param color Optional color parameter
 */
export async function addToCartWithOptions(productId: string, quantity: number = 1, size?: string, color?: string) {
  const payload: any = {
    productId,
    quantity
  };

  if (size) {
    payload.size = size;
  }

  if (color) {
    payload.color = color;
  }

  const response = await post(CART_ENDPOINTS.ADD, payload);
  return response;
}

/**
 * Update cart item quantity
 * @param itemId Cart item ID
 * @param quantity New quantity
 * @param size Optional size parameter
 * @param color Optional color parameter
 */
export async function updateCartItem(itemId: number, quantity: number, size?: string, color?: string) {
  const response = await put(`${CART_ENDPOINTS.UPDATE}/${itemId}`, {
    quantity,
    size: size ,
    color: color
  });
  return response;
}

/**
 * Remove item from cart
 * @param itemId Cart item ID
 */
export async function removeFromCart(itemId: number) {
  const response = await del(`${CART_ENDPOINTS.REMOVE}/${itemId}`);
  return response;
}

/**
 * Clear all items from cart
 */
export async function clearCart() {
  const response = await del(CART_ENDPOINTS.CLEAR);
  return response;
}
